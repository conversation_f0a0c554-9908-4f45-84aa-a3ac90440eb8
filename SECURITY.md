# Security Policy for Stalwart

## Supported Versions

We provide security updates for the following versions of Stalwart:

| Version | Supported          |
| ------- | ------------------ |
| 0.12.x  | :white_check_mark: |
| 0.11.x  | :white_check_mark: |
| < 0.10  | :x:                |

## Reporting a Vulnerability

We take the security of Stalwart very seriously. If you believe you've found a security vulnerability, we encourage you to inform us responsibly.

**Do not report security vulnerabilities through public GitHub issues.**

Instead, please send an email to <NAME_EMAIL> (do not use this email for support questions). You should receive a response within 24 hours. If for some reason you do not, please follow up via email to ensure we received your original message.

To help us better understand the nature and scope of the potential issue, please describe as much as you can about the vulnerability:

- Type of issue (e.g. buffer overflow, SQL injection, cross-site scripting, etc.)
- Full paths of source file(s) related to the manifestation of the issue
- The location of the affected source code (tag/branch/commit or direct URL)
- Any special configuration required to reproduce the issue
- Step-by-step instructions to reproduce the issue
- Proof-of-concept or exploit code
- Impact of the issue, including how an attacker might exploit the issue

This information will help us triage your report more quickly.

Our security team will acknowledge your email within 24 hours, and you'll receive a more detailed response to your email within 48 hours indicating the next steps in handling your report.

## Policy

If you follow these guidelines when reporting an issue to us:

- We will acknowledge your report & provide an estimated timeframe for a fix.
- We will notify you when the issue is resolved.
- We will not take legal action against or suspend or terminate your access to the project.

We strive to keep all our users safe and will make our best effort to promptly fix any security issue.

