name: Test

on:
  workflow_dispatch:

jobs:
  style:
    name: Check Style
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Check Style
        run: cargo fmt --all --check

  test:
    name: Test
    needs: style
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install dependencies
        run: |
          sudo apt-get update -y
          curl -LO https://github.com/glauth/glauth/releases/download/v2.2.0/glauth-linux-arm64
          chmod a+rx glauth-linux-arm64
          nohup ./glauth-linux-arm64 -c tests/resources/ldap.cfg &
          curl -Lo minio.deb https://dl.min.io/server/minio/release/linux-amd64/archive/minio_20230629051228.0.0_amd64.deb
          sudo dpkg -i minio.deb
          mkdir ~/minio
          nohup minio server ~/minio --console-address :9090 &
          curl -LO https://dl.min.io/client/mc/release/linux-amd64/mc
          chmod a+rx mc
          ./mc alias set myminio http://localhost:9000 minioadmin minioadmin
          ./mc mb tmp

      - name: Rust Cache
        uses: Swatinem/rust-cache@v2

      - name: JMAP Protocol Tests
        run: cargo test -p jmap_proto -- --nocapture

      - name: IMAP Protocol Tests
        run: cargo test -p imap_proto -- --nocapture

      - name: Full-text search Tests
        run: cargo test -p store -- --nocapture

      - name: Directory Tests
        run: cargo test -p tests directory -- --nocapture

      - name: SMTP Tests
        run: cargo test -p tests smtp -- --nocapture

      - name: IMAP Tests
        run: cargo test -p tests imap -- --nocapture

      - name: JMAP Tests
        run: cargo test -p tests jmap -- --nocapture
