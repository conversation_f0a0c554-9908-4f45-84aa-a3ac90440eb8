name: Generate Sponsors README
on:
  workflow_dispatch:
  schedule:
    - cron: 30 15 * * 0-6
permissions:
  contents: write
jobs:
  deploy:
    if: github.event_name != 'schedule' || !github.event.repository.fork
    runs-on: ubuntu-latest
    steps:
      - name: Checkout 🛎️
        uses: actions/checkout@v2

      - name: Generate Sponsors 💖
        uses: JamesIves/github-sponsors-readme-action@v1
        with:
          token: ${{ secrets.SPONSORS_PAT }}
          file: 'README.md'
          minimum: 2500
          organization: true

      - name: Deploy to GitHub Pages 🚀
        uses: JamesIves/github-pages-deploy-action@v4
        with:
          branch: main
          folder: '.'
