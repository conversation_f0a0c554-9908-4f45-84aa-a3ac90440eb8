{"mailboxIds": null, "size": 1979, "messageId": ["<EMAIL>"], "from": [{"name": "<PERSON>", "email": "<EMAIL>"}], "to": [{"name": "<PERSON> (The Enforcer)", "email": "<EMAIL>"}], "subject": "Map of Argentina with Description", "sentAt": "1998-08-13T07:42:41Z", "bodyStructure": {"headers": [{"name": "Return-Path", "value": " <<EMAIL>>"}, {"name": "Received", "value": " from mailhost.whitehouse.gov ([**************])\n        by heartbeat.whitehouse.gov (8.8.8/8.8.8) with ESMTP id SAA22453\n        for <<EMAIL>>;\n        Mon, 13 Aug 1998 l8:14:23 +1000"}, {"name": "Received", "value": " from the_big_box.whitehouse.gov ([*************])\n        by mailhost.whitehouse.gov (8.8.8/8.8.7) with ESMTP id RAA20366\n        for <EMAIL>; Mon, 13 Aug 1998 17:42:41 +1000"}, {"name": "Date", "value": " Mon, 13 Aug 1998 17:42:41 +1000"}, {"name": "Message-ID", "value": " <<EMAIL>>"}, {"name": "From", "value": " <PERSON> <<EMAIL>>"}, {"name": "To", "value": " <PERSON> (The Enforcer) <PERSON> <<EMAIL>>"}, {"name": "Subject", "value": "  Map of Argentina with Description"}, {"name": "MIME-Version", "value": " 1.0"}, {"name": "Content-Type", "value": " multipart/mixed;\n              boundary=\"DC8------------DC8638F443D87A7F0726DEF7\""}], "type": "multipart/mixed", "subParts": [{"partId": "1", "blobId": "blob_0", "size": 355, "headers": [{"name": "Content-Type", "value": " text/plain; charset=us-ascii"}, {"name": "Content-Transfer-Encoding", "value": " 7bit"}], "type": "text/plain", "charset": "us-ascii"}, {"partId": "2", "blobId": "blob_1", "size": 288, "headers": [{"name": "Content-Type", "value": " image/gif; name=\"map_of_Argentina.gif\""}, {"name": "Content-Transfer-Encoding", "value": " base64"}, {"name": "Content-Disposition", "value": " inline; fi1ename=\"map_of_Argentina.gif\""}], "name": "map_of_Argentina.gif", "type": "image/gif", "disposition": "inline"}]}, "bodyValues": {"1": {"value": "Hi <PERSON>,\n\nI finally figured out this MIME thing.  Pretty cool.  I'll send you\nsome sax music in .au...", "isEncodingProblem": false, "isTruncated": true}}, "textBody": [{"partId": "1", "blobId": "blob_0", "size": 355, "headers": [{"name": "Content-Type", "value": " text/plain; charset=us-ascii"}, {"name": "Content-Transfer-Encoding", "value": " 7bit"}], "type": "text/plain", "charset": "us-ascii"}, {"partId": "2", "blobId": "blob_1", "size": 288, "headers": [{"name": "Content-Type", "value": " image/gif; name=\"map_of_Argentina.gif\""}, {"name": "Content-Transfer-Encoding", "value": " base64"}, {"name": "Content-Disposition", "value": " inline; fi1ename=\"map_of_Argentina.gif\""}], "name": "map_of_Argentina.gif", "type": "image/gif", "disposition": "inline"}], "htmlBody": [{"partId": "1", "blobId": "blob_0", "size": 355, "headers": [{"name": "Content-Type", "value": " text/plain; charset=us-ascii"}, {"name": "Content-Transfer-Encoding", "value": " 7bit"}], "type": "text/plain", "charset": "us-ascii"}, {"partId": "2", "blobId": "blob_1", "size": 288, "headers": [{"name": "Content-Type", "value": " image/gif; name=\"map_of_Argentina.gif\""}, {"name": "Content-Transfer-Encoding", "value": " base64"}, {"name": "Content-Disposition", "value": " inline; fi1ename=\"map_of_Argentina.gif\""}], "name": "map_of_Argentina.gif", "type": "image/gif", "disposition": "inline"}], "attachments": [{"partId": "2", "blobId": "blob_1", "size": 288, "headers": [{"name": "Content-Type", "value": " image/gif; name=\"map_of_Argentina.gif\""}, {"name": "Content-Transfer-Encoding", "value": " base64"}, {"name": "Content-Disposition", "value": " inline; fi1ename=\"map_of_Argentina.gif\""}], "name": "map_of_Argentina.gif", "type": "image/gif", "disposition": "inline"}], "hasAttachment": false, "preview": "Hi <PERSON>,\n\nI finally figured out this MIME thing.  Pretty cool.  I'll send you\nsome sax music in .au files next week!\n\nAnyway, the attached image is really too small to get a good look at\nArgentina.  Try this for a much better map:\n\n     http://www.1one1yp..."}