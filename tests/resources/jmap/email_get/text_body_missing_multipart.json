{"mailboxIds": {"a": true}, "keywords": {"tag": true}, "size": 4726, "receivedAt": "2119-10-06T01:46:40Z", "messageId": ["<EMAIL>"], "from": [{"name": "<PERSON>", "email": "<EMAIL>"}], "to": [{"name": "<PERSON>", "email": "<EMAIL>"}], "subject": "Test message from Microsoft Outlook 00", "sentAt": "2000-05-17T23:36:13Z", "bodyStructure": {"headers": [{"name": "From", "value": " \"<PERSON>\" <<EMAIL>>"}, {"name": "To", "value": " \"<PERSON>low\" <<EMAIL>>"}, {"name": "Subject", "value": " Test message from Microsoft Outlook 00"}, {"name": "Date", "value": " Wed, 17 May 2000 19:36:13 -0400"}, {"name": "Message-ID", "value": " <<EMAIL>>"}, {"name": "MIME-Version", "value": " 1.0"}, {"name": "Content-Type", "value": " multipart/mixed;\n\tboundary=\"----=_NextPart_000_0004_01BFC037.28F2FA90\""}, {"name": "X-Priority", "value": " 3 (Normal)"}, {"name": "X-MSMail-Priority", "value": " Normal"}, {"name": "X-Mailer", "value": " Microsoft Outlook IMO, Build 9.0.2416 (9.0.2910.0)"}, {"name": "Importance", "value": " Normal"}, {"name": "X-MimeOLE", "value": " Produced By Microsoft MimeOLE V5.00.2314.1300"}], "type": "multipart/mixed", "subParts": [{"partId": "1", "blobId": "blob_0", "size": 1325, "headers": [{"name": "Content-Type", "value": " image/png;\n\tname=\"blueball.png\""}, {"name": "Content-Transfer-Encoding", "value": " base64"}, {"name": "Content-Disposition", "value": " attachment;\n\tfilename=\"blueball.png\""}], "name": "blueball.png", "type": "image/png", "disposition": "attachment"}, {"partId": "2", "blobId": "blob_1", "size": 1453, "headers": [{"name": "Content-Type", "value": " image/png;\n\tname=\"redball.png\""}, {"name": "Content-Transfer-Encoding", "value": " base64"}, {"name": "Content-Disposition", "value": " attachment;\n\tfilename=\"redball.png\""}], "name": "redball.png", "type": "image/png", "disposition": "attachment"}]}, "bodyValues": {}, "textBody": [], "htmlBody": [], "attachments": [{"partId": "1", "blobId": "blob_0", "size": 1325, "headers": [{"name": "Content-Type", "value": " image/png;\n\tname=\"blueball.png\""}, {"name": "Content-Transfer-Encoding", "value": " base64"}, {"name": "Content-Disposition", "value": " attachment;\n\tfilename=\"blueball.png\""}], "name": "blueball.png", "type": "image/png", "disposition": "attachment"}, {"partId": "2", "blobId": "blob_1", "size": 1453, "headers": [{"name": "Content-Type", "value": " image/png;\n\tname=\"redball.png\""}, {"name": "Content-Transfer-Encoding", "value": " base64"}, {"name": "Content-Disposition", "value": " attachment;\n\tfilename=\"redball.png\""}], "name": "redball.png", "type": "image/png", "disposition": "attachment"}], "hasAttachment": true}