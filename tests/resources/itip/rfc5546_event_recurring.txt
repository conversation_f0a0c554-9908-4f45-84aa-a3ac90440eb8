# RFC5546 - Group Event Request

# A sample meeting request is sent from "A" to "B", "C", and "D".
> put <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VEVENT
UID:<EMAIL>
SEQUENCE:0
RRULE:FREQ=MONTHLY;BYMONTHDAY=1;UNTIL=19980901T210000Z
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
DESCRIPTION:IETF-C&S Conference Call
CLASS:PUBLIC
SUMMARY:IETF Calendaring Working Group Meeting
DTSTART:19970601T210000Z
DTEND:19970601T220000Z
LOCATION:Conference Call
DTSTAMP:19970526T083000Z
STATUS:CONFIRMED
END:VEVENT
END:VCALENDAR

> expect
from: <EMAIL>
to: <EMAIL>, <EMAIL>, <EMAIL>
changes: 
BEGIN:VCALENDAR
METHOD:REQUEST
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VEVENT
CLASS:PUBLIC
DESCRIPTION:IETF-C&S Conference Call
LOCATION:Conference Call
STATUS:CONFIRMED
SUMMARY:IETF Calendaring Working Group Meeting
DTEND:19970601T220000Z
DTSTART:19970601T210000Z
ATTENDEE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
RRULE:FREQ=MONTHLY;UNTIL=19980901T210000Z;BYMONTHDAY=1
DTSTAMP:0
SEQUENCE:1
END:VEVENT
END:VCALENDAR

# Send iTIP request to attendees
> send

# Change a recurrence instance
> put <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VEVENT
UID:<EMAIL>
SEQUENCE:2
RRULE:FREQ=MONTHLY;BYMONTHDAY=1;UNTIL=19980901T210000Z
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
DESCRIPTION:IETF-C&S Conference Call
CLASS:PUBLIC
SUMMARY:IETF Calendaring Working Group Meeting
DTSTART:19970601T210000Z
DTEND:19970601T220000Z
LOCATION:Conference Call
DTSTAMP:19970526T083000Z
STATUS:CONFIRMED
END:VEVENT
BEGIN:VEVENT
UID:<EMAIL>
RECURRENCE-ID:19970701T210000Z
SEQUENCE:1
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
DESCRIPTION:IETF-C&S Conference Call
CLASS:PUBLIC
SUMMARY:IETF Calendaring Working Group Meeting
DTSTART:19970703T210000Z
DTEND:19970703T220000Z
LOCATION:Conference Call
DTSTAMP:19970626T093000Z
STATUS:CONFIRMED
END:VEVENT
END:VCALENDAR

> expect
from: <EMAIL>
to: <EMAIL>, <EMAIL>, <EMAIL>
changes: 
BEGIN:VCALENDAR
METHOD:ADD
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VEVENT
CLASS:PUBLIC
DESCRIPTION:IETF-C&S Conference Call
LOCATION:Conference Call
STATUS:CONFIRMED
SUMMARY:IETF Calendaring Working Group Meeting
DTEND:19970703T220000Z
DTSTART:19970703T210000Z
ATTENDEE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
RECURRENCE-ID:19970701T210000Z
UID:<EMAIL>
DTSTAMP:0
SEQUENCE:2
END:VEVENT
END:VCALENDAR

# Send iTIP update to attendees
> send

# Cancel a recurrence instance only for B
> put <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VEVENT
UID:<EMAIL>
SEQUENCE:3
RRULE:FREQ=MONTHLY;BYMONTHDAY=1;UNTIL=19980901T210000Z
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
DESCRIPTION:IETF-C&S Conference Call
CLASS:PUBLIC
SUMMARY:IETF Calendaring Working Group Meeting
DTSTART:19970601T210000Z
DTEND:19970601T220000Z
LOCATION:Conference Call
DTSTAMP:19970526T083000Z
STATUS:CONFIRMED
END:VEVENT
BEGIN:VEVENT
UID:<EMAIL>
RECURRENCE-ID:19970701T210000Z
SEQUENCE:2
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
DESCRIPTION:IETF-C&S Conference Call
CLASS:PUBLIC
SUMMARY:IETF Calendaring Working Group Meeting
DTSTART:19970703T210000Z
DTEND:19970703T220000Z
LOCATION:Conference Call
DTSTAMP:19970626T093000Z
STATUS:CONFIRMED
END:VEVENT
BEGIN:VEVENT
UID:<EMAIL>
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
RECURRENCE-ID:19970801T210000Z
SEQUENCE:2
STATUS:CANCELLED
DTSTAMP:19970721T093000Z
END:VEVENT
END:VCALENDAR

> expect
from: <EMAIL>
to: <EMAIL>
changes: 
BEGIN:VCALENDAR
METHOD:CANCEL
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VEVENT
STATUS:CANCELLED
ATTENDEE:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
RECURRENCE-ID:19970801T210000Z
UID:<EMAIL>
DTSTAMP:0
SEQUENCE:3
END:VEVENT
END:VCALENDAR

# Send iTIP cancellation to B
> send

# Make sure B has the cancelled event
> get <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VEVENT
CLASS:PUBLIC
DESCRIPTION:IETF-C&S Conference Call
LOCATION:Conference Call
STATUS:CONFIRMED
SUMMARY:IETF Calendaring Working Group Meeting
DTEND:19970601T220000Z
DTSTART:19970601T210000Z
ATTENDEE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
RRULE:FREQ=MONTHLY;UNTIL=19980901T210000Z;BYMONTHDAY=1
DTSTAMP:0
SEQUENCE:1
END:VEVENT
BEGIN:VEVENT
CLASS:PUBLIC
DESCRIPTION:IETF-C&S Conference Call
LOCATION:Conference Call
STATUS:CONFIRMED
SUMMARY:IETF Calendaring Working Group Meeting
DTEND:19970703T220000Z
DTSTART:19970703T210000Z
ATTENDEE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
RECURRENCE-ID:19970701T210000Z
UID:<EMAIL>
DTSTAMP:0
SEQUENCE:2
END:VEVENT
BEGIN:VEVENT
STATUS:CANCELLED
ATTENDEE:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
RECURRENCE-ID:19970801T210000Z
UID:<EMAIL>
DTSTAMP:0
SEQUENCE:3
END:VEVENT
END:VCALENDAR

# Change all future instances
> put <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VEVENT
UID:<EMAIL>
SEQUENCE:4
RRULE:FREQ=MONTHLY;BYMONTHDAY=1;UNTIL=19980901T210000Z
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
DESCRIPTION:IETF-C&S Conference Call
CLASS:PUBLIC
SUMMARY:IETF Calendaring Working Group Meeting
DTSTART:19970601T210000Z
DTEND:19970601T220000Z
LOCATION:Conference Call
DTSTAMP:19970526T083000Z
STATUS:CONFIRMED
END:VEVENT
BEGIN:VEVENT
UID:<EMAIL>
RECURRENCE-ID:19970701T210000Z
SEQUENCE:3
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
DESCRIPTION:IETF-C&S Conference Call
CLASS:PUBLIC
SUMMARY:IETF Calendaring Working Group Meeting
DTSTART:19970703T210000Z
DTEND:19970703T220000Z
LOCATION:Conference Call
DTSTAMP:19970626T093000Z
STATUS:CONFIRMED
END:VEVENT
BEGIN:VEVENT
UID:<EMAIL>
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
RECURRENCE-ID:19970801T210000Z
SEQUENCE:3
STATUS:CANCELLED
DTSTAMP:19970721T093000Z
END:VEVENT
BEGIN:VEVENT
UID:<EMAIL>
RECURRENCE-ID;THISANDFUTURE:19970901T210000Z
SEQUENCE:3
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE:mailto:<EMAIL>
DESCRIPTION:IETF-C&S Discussion
CLASS:PUBLIC
SUMMARY:IETF Calendaring Working Group Meeting
DTSTART:19970901T210000Z
DTEND:19970901T220000Z
LOCATION:Building 32, Microsoft, Seattle, WA
DTSTAMP:19970526T083000Z
STATUS:CONFIRMED
END:VEVENT
END:VCALENDAR

> expect
from: <EMAIL>
to: <EMAIL>, <EMAIL>, <EMAIL>
changes: 
BEGIN:VCALENDAR
METHOD:ADD
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VEVENT
CLASS:PUBLIC
DESCRIPTION:IETF-C&S Discussion
LOCATION:Building 32\, Microsoft\, Seattle\, WA
STATUS:CONFIRMED
SUMMARY:IETF Calendaring Working Group Meeting
DTEND:19970901T220000Z
DTSTART:19970901T210000Z
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
RECURRENCE-ID;THISANDFUTURE:19970901T210000Z
UID:<EMAIL>
DTSTAMP:0
SEQUENCE:4
END:VEVENT
END:VCALENDAR

# Send iTIP update to attendees
> send

# Make sure B has the complete event including all updates
> get <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VEVENT
CLASS:PUBLIC
DESCRIPTION:IETF-C&S Conference Call
LOCATION:Conference Call
STATUS:CONFIRMED
SUMMARY:IETF Calendaring Working Group Meeting
DTEND:19970601T220000Z
DTSTART:19970601T210000Z
ATTENDEE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
RRULE:FREQ=MONTHLY;UNTIL=19980901T210000Z;BYMONTHDAY=1
DTSTAMP:0
SEQUENCE:1
END:VEVENT
BEGIN:VEVENT
CLASS:PUBLIC
DESCRIPTION:IETF-C&S Conference Call
LOCATION:Conference Call
STATUS:CONFIRMED
SUMMARY:IETF Calendaring Working Group Meeting
DTEND:19970703T220000Z
DTSTART:19970703T210000Z
ATTENDEE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
RECURRENCE-ID:19970701T210000Z
UID:<EMAIL>
DTSTAMP:0
SEQUENCE:2
END:VEVENT
BEGIN:VEVENT
STATUS:CANCELLED
ATTENDEE:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
RECURRENCE-ID:19970801T210000Z
UID:<EMAIL>
DTSTAMP:0
SEQUENCE:3
END:VEVENT
BEGIN:VEVENT
CLASS:PUBLIC
DESCRIPTION:IETF-C&S Discussion
LOCATION:Building 32\, Microsoft\, Seattle\, WA
STATUS:CONFIRMED
SUMMARY:IETF Calendaring Working Group Meeting
DTEND:19970901T220000Z
DTSTART:19970901T210000Z
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
RECURRENCE-ID;THISANDFUTURE:19970901T210000Z
UID:<EMAIL>
DTSTAMP:0
SEQUENCE:4
END:VEVENT
END:VCALENDAR

# Cancel the recurring event
> put <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VEVENT
UID:<EMAIL>
SEQUENCE:4
RRULE:FREQ=MONTHLY;BYMONTHDAY=1;UNTIL=19980901T210000Z
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
DESCRIPTION:IETF-C&S Conference Call
CLASS:PUBLIC
SUMMARY:IETF Calendaring Working Group Meeting
DTSTART:19970601T210000Z
DTEND:19970601T220000Z
LOCATION:Conference Call
DTSTAMP:19970526T083000Z
STATUS:CONFIRMED
END:VEVENT
BEGIN:VEVENT
UID:<EMAIL>
RECURRENCE-ID:19970701T210000Z
SEQUENCE:3
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
DESCRIPTION:IETF-C&S Conference Call
CLASS:PUBLIC
SUMMARY:IETF Calendaring Working Group Meeting
DTSTART:19970703T210000Z
DTEND:19970703T220000Z
LOCATION:Conference Call
DTSTAMP:19970626T093000Z
STATUS:CONFIRMED
END:VEVENT
BEGIN:VEVENT
UID:<EMAIL>
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
RECURRENCE-ID:19970801T210000Z
SEQUENCE:3
STATUS:CANCELLED
DTSTAMP:19970721T093000Z
END:VEVENT
BEGIN:VEVENT
UID:<EMAIL>
RECURRENCE-ID;THISANDFUTURE:19970901T210000Z
SEQUENCE:3
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE:mailto:<EMAIL>
DESCRIPTION:IETF-C&S Discussion
CLASS:PUBLIC
SUMMARY:IETF Calendaring Working Group Meeting
DTSTART:19970901T210000Z
DTEND:19970901T220000Z
LOCATION:Building 32, Microsoft, Seattle, WA
DTSTAMP:19970526T083000Z
STATUS:CONFIRMED
END:VEVENT
END:VCALENDAR

# Cancel a recurring event
> delete <EMAIL> <EMAIL>

> expect
from: <EMAIL>
to: <EMAIL>, <EMAIL>, <EMAIL>
changes: 
BEGIN:VCALENDAR
METHOD:CANCEL
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VEVENT
STATUS:CANCELLED
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
DTSTAMP:0
SEQUENCE:5
END:VEVENT
END:VCALENDAR

# Send iTIP cancellation to attendees
> send

# Make sure all instances in B were deleted
> get <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VEVENT
CLASS:PUBLIC
DESCRIPTION:IETF-C&S Conference Call
LOCATION:Conference Call
STATUS:CANCELLED
SUMMARY:IETF Calendaring Working Group Meeting
DTEND:19970601T220000Z
DTSTART:19970601T210000Z
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
RRULE:FREQ=MONTHLY;UNTIL=19980901T210000Z;BYMONTHDAY=1
DTSTAMP:0
END:VEVENT
BEGIN:VEVENT
CLASS:PUBLIC
DESCRIPTION:IETF-C&S Conference Call
LOCATION:Conference Call
STATUS:CANCELLED
SUMMARY:IETF Calendaring Working Group Meeting
DTEND:19970703T220000Z
DTSTART:19970703T210000Z
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
RECURRENCE-ID:19970701T210000Z
UID:<EMAIL>
DTSTAMP:0
SEQUENCE:2
END:VEVENT
BEGIN:VEVENT
STATUS:CANCELLED
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
RECURRENCE-ID:19970801T210000Z
UID:<EMAIL>
DTSTAMP:0
SEQUENCE:3
END:VEVENT
BEGIN:VEVENT
CLASS:PUBLIC
DESCRIPTION:IETF-C&S Discussion
LOCATION:Building 32\, Microsoft\, Seattle\, WA
STATUS:CANCELLED
SUMMARY:IETF Calendaring Working Group Meeting
DTEND:19970901T220000Z
DTSTART:19970901T210000Z
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ATTENDEE:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
RECURRENCE-ID;THISANDFUTURE:19970901T210000Z
UID:<EMAIL>
DTSTAMP:0
SEQUENCE:4
END:VEVENT
END:VCALENDAR

# Add a new series of instances to the recurring event
> put <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VEVENT
UID:<EMAIL>
SEQUENCE:0
RRULE:WKST=SU;BYDAY=TU;FREQ=WEEKLY
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE:mailto:<EMAIL>
SUMMARY:Review Accounts
DTSTART:19980303T210000Z
DTEND:19980303T220000Z
LOCATION:The White Room
DTSTAMP:19980301T093000Z
STATUS:CONFIRMED
END:VEVENT
BEGIN:VEVENT
UID:<EMAIL>
SEQUENCE:2
RECURRENCE-ID;THISANDFUTURE:19970901T210000Z
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE:mailto:<EMAIL>
SUMMARY:Review Accounts
DTSTAMP:19980303T193000Z
LOCATION:The Red Room
STATUS:CONFIRMED
END:VEVENT
END:VCALENDAR

> expect
from: <EMAIL>
to: <EMAIL>, <EMAIL>
changes: 
BEGIN:VCALENDAR
METHOD:REQUEST
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VEVENT
LOCATION:The White Room
STATUS:CONFIRMED
SUMMARY:Review Accounts
DTEND:19980303T220000Z
DTSTART:19980303T210000Z
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
RRULE:FREQ=WEEKLY;BYDAY=TU;WKST=SU
DTSTAMP:0
SEQUENCE:1
END:VEVENT
BEGIN:VEVENT
LOCATION:The Red Room
STATUS:CONFIRMED
SUMMARY:Review Accounts
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
RECURRENCE-ID;THISANDFUTURE:19970901T210000Z
UID:<EMAIL>
DTSTAMP:0
SEQUENCE:3
END:VEVENT
END:VCALENDAR

# Send iTIP request to B and C
> send

# Add a new series of instances to the recurring event (update)
> put <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VEVENT
UID:<EMAIL>
SEQUENCE:2
RRULE:WKST=SU;BYDAY=TU,TH;FREQ=WEEKLY
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE:mailto:<EMAIL>
SUMMARY:Review Accounts
DTSTART:19980303T210000Z
DTEND:19980303T220000Z
DTSTAMP:19980303T193000Z
LOCATION:The White Room
STATUS:CONFIRMED
END:VCALENDAR

> expect
from: <EMAIL>
to: <EMAIL>
changes: EXDATE, RRULE
BEGIN:VCALENDAR
METHOD:REQUEST
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VEVENT
LOCATION:The White Room
STATUS:CONFIRMED
SUMMARY:Review Accounts
DTEND:19980303T220000Z
DTSTART:19980303T210000Z
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
RRULE:FREQ=WEEKLY;BYDAY=TU,TH;WKST=SU
DTSTAMP:0
SEQUENCE:3
END:VEVENT
END:VCALENDAR
================================
from: <EMAIL>
to: <EMAIL>
changes: 
BEGIN:VCALENDAR
METHOD:CANCEL
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VEVENT
STATUS:CANCELLED
ATTENDEE:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
RECURRENCE-ID;THISANDFUTURE:19970901T210000Z
UID:<EMAIL>
DTSTAMP:0
SEQUENCE:4
END:VEVENT
END:VCALENDAR

# Send iTIP request to B and cancellation to C
> send

# Make sure B has the updated event
> get <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VEVENT
LOCATION:The White Room
STATUS:CONFIRMED
SUMMARY:Review Accounts
DTEND:19980303T220000Z
DTSTART:19980303T210000Z
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
RRULE:FREQ=WEEKLY;BYDAY=TU,TH;WKST=SU
DTSTAMP:0
SEQUENCE:3
END:VEVENT
END:VCALENDAR

# Make sure C has the updated event
> get <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VEVENT
LOCATION:The Red Room
STATUS:CANCELLED
SUMMARY:Review Accounts
ATTENDEE:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
RECURRENCE-ID;THISANDFUTURE:19970901T210000Z
UID:<EMAIL>
DTSTAMP:0
END:VEVENT
BEGIN:VEVENT
LOCATION:The White Room
STATUS:CONFIRMED
SUMMARY:Review Accounts
DTEND:19980303T220000Z
DTSTART:19980303T210000Z
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
RRULE:FREQ=WEEKLY;BYDAY=TU;WKST=SU
DTSTAMP:0
SEQUENCE:1
END:VEVENT
END:VCALENDAR

# Delete the event from B
> delete <EMAIL> <EMAIL>

> expect
from: <EMAIL>
to: <EMAIL>
changes: 
BEGIN:VCALENDAR
METHOD:REPLY
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VEVENT
ATTENDEE;PARTSTAT=DECLINED:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
DTSTAMP:0
SEQUENCE:3
END:VEVENT
END:VCALENDAR

> send

# Make sure A has the cancellation from B
> get <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VEVENT
LOCATION:The White Room
STATUS:CONFIRMED
SUMMARY:Review Accounts
DTEND:19980303T220000Z
DTSTART:19980303T210000Z
ATTENDEE;PARTSTAT=DECLINED:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
RRULE:FREQ=WEEKLY;BYDAY=TU,TH;WKST=SU
DTSTAMP:1
SEQUENCE:3
END:VEVENT
END:VCALENDAR

