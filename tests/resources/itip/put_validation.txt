# Scheduling invalid actions

# Event has no scheduling information
> put <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VEVENT
DTSTAMP:19970611T190000Z
DTSTART:19970701T200000Z
DTEND:19970701T2100000Z
SUMMARY:Conference
UID:<EMAIL>
SEQUENCE:0
STATUS:CONFIRMED
END:VEVENT
END:VCALENDAR

> expect
NoSchedulingInfo

> reset

# X is not the organizer
> put <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VEVENT
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED;CN=A:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL;CN=B:mailto:<EMAIL>
DTSTAMP:19970611T190000Z
DTSTART:19970701T200000Z
DTEND:19970701T2100000Z
SUMMARY:Conference
UID:<EMAIL>
SEQUENCE:0
STATUS:CONFIRMED
END:VEVENT
END:VCALENDAR

> expect
NotOrganizerNorAttendee

> reset

# No attendees
> put <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VEVENT
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED;CN=A:mailto:<EMAIL>
DTSTAMP:19970611T190000Z
DTSTART:19970701T200000Z
DTEND:19970701T2100000Z
SUMMARY:Conference
UID:<EMAIL>
SEQUENCE:0
STATUS:CONFIRMED
END:VEVENT
END:VCALENDAR

> expect
NothingToSend

> reset


# Organizer with client scheduling agent
> put <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VEVENT
ORGANIZER;SCHEDULE-AGENT=CLIENT:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED;CN=A:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL;CN=B:mailto:<EMAIL>
DTSTAMP:19970611T190000Z
DTSTART:19970701T200000Z
DTEND:19970701T2100000Z
SUMMARY:Conference
UID:<EMAIL>
SEQUENCE:0
STATUS:CONFIRMED
END:VEVENT
END:VCALENDAR

> expect
OtherSchedulingAgent

> reset

# Single participant with client scheduling agent
> put <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VEVENT
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED;CN=A:mailto:<EMAIL>
ATTENDEE;SCHEDULE-AGENT=CLIENT;RSVP=TRUE;CUTYPE=INDIVIDUAL;CN=B:mailto:<EMAIL>
DTSTAMP:19970611T190000Z
DTSTART:19970701T200000Z
DTEND:19970701T2100000Z
SUMMARY:Conference
UID:<EMAIL>
SEQUENCE:0
STATUS:CONFIRMED
END:VEVENT
END:VCALENDAR

> expect
NothingToSend

> reset

# Only send updates to clients with server scheduling agent
> put <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VEVENT
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED;CN=A:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL;CN=B:mailto:<EMAIL>
ATTENDEE;SCHEDULE-AGENT=CLIENT;RSVP=TRUE;CUTYPE=INDIVIDUAL;CN=B:mailto:<EMAIL>
DTSTAMP:19970611T190000Z
DTSTART:19970701T200000Z
DTEND:19970701T2100000Z
SUMMARY:Conference
UID:<EMAIL>
SEQUENCE:0
STATUS:CONFIRMED
END:VEVENT
END:VCALENDAR

> expect
from: <EMAIL>
to: <EMAIL>
changes: 
BEGIN:VCALENDAR
METHOD:REQUEST
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VEVENT
STATUS:CONFIRMED
SUMMARY:Conference
DTEND:19970701T2100000Z
DTSTART:19970701T200000Z
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED;CN=A:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL;CN=B;PARTSTAT=NEEDS-ACTION:mailto:b@exa
 mple.com
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL;CN=B;PARTSTAT=NEEDS-ACTION:mailto:d@exa
 mple.com
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
DTSTAMP:0
SEQUENCE:1
END:VEVENT
END:VCALENDAR

> reset

# Multiple object types should be rejected
> put <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VEVENT
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED;CN=A:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL;CN=B:mailto:<EMAIL>
DTSTAMP:19970611T190000Z
DTSTART:19970701T200000Z
DTEND:19970701T2100000Z
SUMMARY:Conference
UID:<EMAIL>
SEQUENCE:0
STATUS:CONFIRMED
END:VEVENT
BEGIN:VTODO
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED;CN=A:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL;CN=B:mailto:<EMAIL>
DTSTAMP:19970611T190000Z
DTSTART:19970701T200000Z
DTEND:19970701T2100000Z
SUMMARY:Conference
UID:<EMAIL>
SEQUENCE:0
STATUS:CONFIRMED
END:VTODO
END:VCALENDAR

> expect
MultipleObjectTypes

> reset

# Multiple organizers should be rejected
> put <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VEVENT
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED;CN=A:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL;CN=B:mailto:<EMAIL>
DTSTAMP:19970611T190000Z
DTSTART:19970701T200000Z
DTEND:19970701T2100000Z
SUMMARY:Conference
UID:<EMAIL>
SEQUENCE:0
STATUS:CONFIRMED
END:VEVENT
BEGIN:VEVENT
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED;CN=D:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL;CN=B:mailto:<EMAIL>
DTSTAMP:19970611T190000Z
DTSTART:19970701T200000Z
DTEND:19970701T2100000Z
SUMMARY:Conference
UID:<EMAIL>
SEQUENCE:0
STATUS:CONFIRMED
END:VEVENT
END:VCALENDAR

> expect
MultipleOrganizer

> reset

# Different UIDs should be rejected
> put <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VEVENT
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED;CN=A:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL;CN=B:mailto:<EMAIL>
DTSTAMP:19970611T190000Z
DTSTART:19970701T200000Z
DTEND:19970701T2100000Z
SUMMARY:Conference
UID:<EMAIL>
SEQUENCE:0
STATUS:CONFIRMED
END:VEVENT
BEGIN:VEVENT
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED;CN=A:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL;CN=B:mailto:<EMAIL>
DTSTAMP:19970611T190000Z
DTSTART:19970701T200000Z
DTEND:19970701T2100000Z
SUMMARY:Conference
UID:<EMAIL>
SEQUENCE:0
STATUS:CONFIRMED
END:VEVENT
END:VCALENDAR

> expect
MultipleUid

> reset

# Multiple object instances should be rejected
> put <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VEVENT
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED;CN=A:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL;CN=B:mailto:<EMAIL>
DTSTAMP:19970611T190000Z
DTSTART:19970701T200000Z
DTEND:19970701T2100000Z
SUMMARY:Conference
UID:<EMAIL>
SEQUENCE:0
STATUS:CONFIRMED
END:VEVENT
BEGIN:VEVENT
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED;CN=A:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL;CN=B:mailto:<EMAIL>
DTSTAMP:19970611T190000Z
DTSTART:19970701T200000Z
DTEND:19970701T2100000Z
RECURRENCE-ID:19970701T200000Z
SUMMARY:Conference
UID:<EMAIL>
SEQUENCE:0
STATUS:CONFIRMED
END:VEVENT
BEGIN:VEVENT
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED;CN=A:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL;CN=B:mailto:<EMAIL>
DTSTAMP:19970611T190000Z
DTSTART:19970701T200000Z
DTEND:19970701T2100000Z
RECURRENCE-ID:19970701T200000Z
SUMMARY:Conference
UID:<EMAIL>
SEQUENCE:0
STATUS:CONFIRMED
END:VEVENT
END:VCALENDAR

> expect
MultipleObjectInstances

