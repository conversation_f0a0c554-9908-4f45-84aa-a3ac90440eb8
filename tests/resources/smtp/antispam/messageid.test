expect MISSING_MID

X-Message-ID: <<EMAIL>>

Test
<!-- NEXT TEST -->
expect MID_RHS_IP_LITERAL

Message-ID: <hello@[127.0.0.1]>

Test
<!-- NEXT TEST -->
expect MID_BARE_IP

Message-ID: <hello@127.0.0.1>

Test
<!-- NEXT TEST -->
expect MID_RHS_NOT_FQDN

Message-ID: <hello@domain>

Test
<!-- NEXT TEST -->
expect MID_RHS_WWW

Message-ID: <<EMAIL>>

Test
<!-- NEXT TEST -->
expect INVALID_MSGID

Message-ID: <@domain.com>

Test
<!-- NEXT TEST -->
expect INVALID_MSGID

Message-ID: <hélló@domain.com>

Test

<!-- NEXT TEST -->
expect INVALID_MSGID

Message-ID: <<EMAIL>> (hello world)

Test
<!-- NEXT TEST -->
expect MID_RHS_TOO_LONG

Message-ID: <<EMAIL>>

Test
<!-- NEXT TEST -->
expect MID_MISSING_BRACKETS

Message-ID: <EMAIL>

Test
<!-- NEXT TEST -->
expect MID_CONTAINS_FROM

From: <<EMAIL>>
Message-ID: <<EMAIL>>

Test
<!-- NEXT TEST -->
expect MID_RHS_MATCH_FROM

From: <<EMAIL>>
Message-ID: <<EMAIL>>

Test
<!-- NEXT TEST -->
expect MID_RHS_MATCH_FROMTLD

From: <<EMAIL>>
Message-ID: <<EMAIL>>

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect MID_RHS_MATCH_ENV_FROMTLD

Message-ID: <<EMAIL>>

Test
<!-- NEXT TEST -->
expect MID_CONTAINS_TO

To: User <<EMAIL>>
Message-ID: <<EMAIL>>

Test
<!-- NEXT TEST -->
expect MID_RHS_MATCH_TO

From: Myself <<EMAIL>>
To: User <<EMAIL>>
Cc: John <<EMAIL>>, Jane <<EMAIL>>, Bill <<EMAIL>>
Message-ID: <<EMAIL>>

Test
