# docker-compose -f docker-compose-pebble.yaml up
# curl --request POST --data '{"ip":"***********"}' http://localhost:8055/set-default-ipv4
# HTTPS port should be 5001
# HTTP port should be 5002
# Directory https://localhost:14000/dir

version: '3'
services:
  pebble:
    image: letsencrypt/pebble:latest
    command: pebble -config /test/config/pebble-config.json -strict -dnsserver **********:8053 #-dnsserver *******:53
    ports:
      - 14000:14000 # HTTPS ACME API
      - 15000:15000 # HTTPS Management API
    networks:
      acmenet:
        ipv4_address: **********
  challtestsrv:
    image: letsencrypt/pebble-challtestsrv:latest
    command: pebble-challtestsrv -defaultIPv6 "" -defaultIPv4 **********
    ports:
      - 8055:8055 # HTTP Management API
    networks:
      acmenet:
        ipv4_address: **********

networks:
  acmenet:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/24
