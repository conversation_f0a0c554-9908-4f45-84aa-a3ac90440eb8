/*
 * SPDX-FileCopyrightText: 2020 Stalwart Labs LLC <<EMAIL>>
 *
 * SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-SEL
 */

use common::Core;

use store::Stores;
use utils::config::Config;

use smtp::core::Session;

use crate::{
    AssertConfig,
    smtp::{
        TempDir, TestSMTP,
        session::{TestSession, VerifyResponse},
    },
};

const CONFIG: &str = r#"
[storage]
data = "rocksdb"
lookup = "rocksdb"
blob = "rocksdb"
fts = "rocksdb"
directory = "local"

[store."rocksdb"]
type = "rocksdb"
path = "{TMP}/data.db"

[directory."local"]
type = "memory"

[[directory."local".principals]]
name = "john"
description = "<PERSON>"
secret = "secret"
email = ["<EMAIL>"]
email-list = ["<EMAIL>"]

[[directory."local".principals]]
name = "jane"
description = "<PERSON>"
secret = "p4ssw0rd"
email = "<EMAIL>"
email-list = ["<EMAIL>"]

[[directory."local".principals]]
name = "bill"
description = "Bill Foobar"
secret = "p4ssw0rd"
email = "<EMAIL>"
email-list = ["<EMAIL>"]

[session.rcpt]
directory = "'local'"

[session.extensions]
vrfy = [{if = "remote_ip = '********'", then = true},
        {else = false}]
expn = [{if = "remote_ip = '********'", then = true},
        {else = false}]

"#;

#[tokio::test]
async fn vrfy_expn() {
    // Enable logging
    crate::enable_logging();

    let tmp_dir = TempDir::new("smtp_vrfy_test", true);
    let mut config = Config::new(tmp_dir.update_config(CONFIG)).unwrap();
    let stores = Stores::parse_all(&mut config, false).await;
    let core = Core::parse(&mut config, stores, Default::default()).await;
    config.assert_no_errors();

    // EHLO should not advertise VRFY/EXPN to ********
    let mut session = Session::test(TestSMTP::from_core(core).server);
    session.data.remote_ip_str = "********".into();
    session.eval_session_params().await;
    session
        .ehlo("mx.foobar.org")
        .await
        .assert_not_contains("EXPN")
        .assert_not_contains("VRFY");
    session.cmd("VRFY john", "252 2.5.1").await;
    session.cmd("EXPN <EMAIL>", "252 2.5.1").await;

    // EHLO should advertise VRFY/EXPN for ********
    session.data.remote_ip_str = "********".into();
    session.eval_session_params().await;
    session
        .ehlo("mx.foobar.org")
        .await
        .assert_contains("EXPN")
        .assert_contains("VRFY");

    // Successful VRFY
    session.cmd("VRFY john", "250 <EMAIL>").await;

    // Successful EXPN
    session
        .cmd("EXPN <EMAIL>", "250")
        .await
        .assert_contains("<EMAIL>")
        .assert_contains("<EMAIL>")
        .assert_contains("250 <EMAIL>");

    // Non-existent VRFY
    session.cmd("VRFY robert", "550 5.1.2").await;

    // Non-existent EXPN
    session.cmd("EXPN procurement", "550 5.1.2").await;
}
