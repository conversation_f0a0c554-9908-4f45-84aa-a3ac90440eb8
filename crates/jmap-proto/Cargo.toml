[package]
name = "jmap_proto"
version = "0.12.4"
edition = "2024"
resolver = "2"

[dependencies]
store = { path = "../store" }
utils = { path = "../utils" }
trc = { path = "../trc" }
mail-parser = { version = "0.11", features = ["full_encoding", "rkyv"] } 
fast-float = "0.2.0"
serde = { version = "1.0", features = ["derive"]}
ahash = { version = "0.8.2", features = ["serde"] }
serde_json = { version = "1.0", features = ["raw_value"] }
hashify = "0.2"
rkyv = { version = "0.8.10", features = ["little_endian"] }
compact_str = { version = "0.9.0", features = ["rkyv", "serde"] }

[dev-dependencies]
tokio = { version = "1.45", features = ["full"] }
