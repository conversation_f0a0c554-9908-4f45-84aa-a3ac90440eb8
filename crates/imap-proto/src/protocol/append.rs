/*
 * SPDX-FileCopyrightText: 2020 Stalwart Labs LLC <<EMAIL>>
 *
 * SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-SEL
 */

use super::Flag;

#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq)]
pub struct Arguments {
    pub tag: String,
    pub mailbox_name: String,
    pub messages: Vec<Message>,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub struct Message {
    pub message: Vec<u8>,
    pub flags: Vec<Flag>,
    pub received_at: Option<i64>,
}
